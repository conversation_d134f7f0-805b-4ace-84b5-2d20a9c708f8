"""
Example usage of the SalesTagger class for tagging sales data with producer IDs.

This example demonstrates how to use the SalesTagger to automatically tag sales
based on keywords found in specified fields.
"""

from firebase_admin import firestore
from tagger.sales_tagger import SalesTagger, SalesDataTagRequest


def example_sales_tagging():
    """
    Example of how to use the SalesTagger class to tag sales data.
    """
    # Initialize Firestore client
    db = firestore.client()
    
    # Create a SalesTagger instance
    sales_tagger = SalesTagger(db)
    
    # Define the tagging configuration
    # This configuration will:
    # - Look at the "title" field in sales documents
    # - Tag sales with "producer_123" if they contain "artisan" or "handmade"
    # - Tag sales with "producer_456" if they contain "vintage" or "antique"
    tag_request = SalesDataTagRequest(
        store_id="store_abc123",
        fields=["title"],  # Fields to search in
        tag_rule={
            "producer_123": ["artisan", "handmade", "craft"],
            "producer_456": ["vintage", "antique", "retro"],
            "producer_789": ["organic", "natural", "eco-friendly"]
        },
        agreements=["agreement_1", "agreement_2"]  # Related agreements
    )
    
    # Execute the tagging
    try:
        result = sales_tagger.tag_sales_data(tag_request)
        
        print("Tagging Results:")
        print(f"Success: {result['success']}")
        print(f"Store ID: {result['store_id']}")
        print(f"Total Processed: {result['total_processed']}")
        print(f"Total Tagged: {result['total_tagged']}")
        print(f"Tag Rules Applied: {result['tag_rules_applied']}")
        
        # Print details for staging collection
        staging_results = result['staging_results']
        print(f"\nStaging Collection ({staging_results['collection']}):")
        print(f"  Processed: {staging_results['processed']}")
        print(f"  Tagged: {staging_results['tagged']}")
        
        for doc in staging_results['tagged_documents']:
            print(f"  - Document {doc['document_id']} tagged with {doc['producer_id']}")
            print(f"    Matched fields: {doc['matched_fields']}")
        
        # Print details for silver collection
        silver_results = result['silver_results']
        print(f"\nSilver Collection ({silver_results['collection']}):")
        print(f"  Processed: {silver_results['processed']}")
        print(f"  Tagged: {silver_results['tagged']}")
        
        for doc in silver_results['tagged_documents']:
            print(f"  - Document {doc['document_id']} tagged with {doc['producer_id']}")
            print(f"    Matched fields: {doc['matched_fields']}")
            
    except Exception as e:
        print(f"Error during tagging: {str(e)}")


def example_multi_field_tagging():
    """
    Example of tagging based on multiple fields.
    """
    db = firestore.client()
    sales_tagger = SalesTagger(db)
    
    # Configuration that searches in both title and vendor fields
    tag_request = SalesDataTagRequest(
        store_id="store_xyz789",
        fields=["title", "vendor"],  # Search in both title and vendor
        tag_rule={
            "producer_001": ["john smith", "smith crafts"],
            "producer_002": ["mary's boutique", "mary johnson"],
            "producer_003": ["green earth", "sustainable goods"]
        },
        agreements=["agreement_3"]
    )
    
    try:
        result = sales_tagger.tag_sales_data(tag_request)
        print(f"Multi-field tagging completed: {result['total_tagged']} items tagged")
        
    except Exception as e:
        print(f"Error during multi-field tagging: {str(e)}")


def example_case_insensitive_matching():
    """
    Example showing that keyword matching is case-insensitive.
    """
    db = firestore.client()
    sales_tagger = SalesTagger(db)
    
    # Keywords will match regardless of case
    tag_request = SalesDataTagRequest(
        store_id="store_case_test",
        fields=["title"],
        tag_rule={
            "producer_case": ["UPPERCASE", "lowercase", "MiXeD cAsE"]
        },
        agreements=["agreement_case"]
    )
    
    try:
        result = sales_tagger.tag_sales_data(tag_request)
        print(f"Case-insensitive tagging completed: {result['total_tagged']} items tagged")
        
    except Exception as e:
        print(f"Error during case-insensitive tagging: {str(e)}")


if __name__ == "__main__":
    print("Sales Tagger Examples")
    print("=" * 50)
    
    print("\n1. Basic Sales Tagging Example:")
    example_sales_tagging()
    
    print("\n2. Multi-field Tagging Example:")
    example_multi_field_tagging()
    
    print("\n3. Case-insensitive Matching Example:")
    example_case_insensitive_matching()
