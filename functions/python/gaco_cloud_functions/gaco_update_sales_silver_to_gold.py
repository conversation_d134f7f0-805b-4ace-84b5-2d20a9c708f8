from firebase_functions.firestore_fn import (
    on_document_written_with_auth_context,
    on_document_written,
    Event, 
    DocumentSnapshot
)
# from models.requests.invoice_request import CreateInvoiceBySaleIdRequest
# from data_generator.single_invoice_data_generator import (
#     generate_single_invoice_data_handler, 
#     generate_single_invoice_data_handler_from_documentId
# )

from firebase_admin import firestore
from firebase_functions import logger, options
from models.sales import SalesSilver, sales_gold_collection
from data_generator.sales_report_data_generator import generate_sales_report_from_sales_silver_handler


def _process_sales_silver_document(
        db: firestore.client, 
        document_id: str,
        sales_silver: SalesSilver
    ):
    """
    Common processing function for sales-silver documents.
    Calculates invoice data and stores it in the sales-invoice collection.
    
    Args:
        document_id: The ID of the sales-silver document
        sales_silver_dict: The sales-silver document data as a dictionary
    """
    sales_gold = generate_sales_report_from_sales_silver_handler(
        sales_silver, 
        document_id
    )

    logger.info(f"Invoice data generated for document {document_id}")

    # Save to sales-gold collection
    db.collection(sales_gold_collection) \
        .document(document_id) \
        .set(sales_gold.model_dump())


@on_document_written(
    document="sales-silver/{document_id}",
    region="europe-west3",
    timeout_sec=300,
    memory=options.MemoryOption.GB_1
)
def on_sales_silver_written(event: Event[DocumentSnapshot]):
    """Cloud function that triggers when a sales-staging document is created."""
    document_id = event.data.id
    sales_silver = event.data.to_dict()
    sales_silver = SalesSilver.model_validate(sales_silver)

    db = firestore.client()
    
    logger.info(f"Processing updated sales-silver document: {document_id}")
    _process_sales_silver_document(db, document_id, sales_silver)


# @https_fn.on_call(
#     region="europe-west3",
#     timeout_sec=300,
#     memory=options.MemoryOption.GB_1
# )
# def on_call_generate_invoice_data(request: https_fn.CallableRequest):
#     db = firestore.client()
#     request = CreateInvoiceBySaleIdRequest.model_validate(request.data)
#     sales_gold = generate_single_invoice_data_handler_from_documentId(db, request.sale_id)
#     return sales_gold.model_dump()
