from firebase_functions import options
from gaco_framework.auth import AuthRequirement
from gaco_framework.context import GacoContext
from gaco_framework.decorators import gaco_endpoint
from models.requests.agreements_requests import (
    ApproveAgreementRequest,
    CreateAgreementRequest,
    DeleteAgreementRequest,
    RejectAgreementRequest,
    SubmitAgreementForApprovalRequest,
    TerminateAgreementRequest,
    UpdateDraftAgreementRequest,
)
from gaco_framework.models import GacoResponse
from services.agreement_manager import AgreementManager
from firebase_functions.firestore_fn import (
    on_document_deleted_with_auth_context,
    Event,
    DocumentSnapshot
)
from firebase_admin import firestore
from constants.collections import partnership_collection
from models.partner import Partnership


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def create_agreement(context: GacoContext, data: dict) -> dict:
    """
    Creates a new draft agreement.

    Authentication:
        - Authenticated user required.
    """
    agreement_request = CreateAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    agreement_id = agreement_manager.create_draft_agreement(agreement_request)
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement created successfully",
        data={"agreement_id": agreement_id}
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def update_draft_agreement(context: GacoContext, data: dict) -> dict:
    """
    Updates an existing draft agreement.

    Authentication:
        - Authenticated user required.
        - User must have permission to update the agreement.
    """
    update_request = UpdateDraftAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    update_data = update_request.model_dump(exclude_unset=True, exclude={'agreement_id'})
    result = agreement_manager.update_agreement(
        update_request.agreement_id, update_data
    )
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement updated successfully",
        data=result
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def submit_agreement_for_approval(context: GacoContext, data: dict) -> dict:
    """
    Submits a draft agreement for approval.

    Authentication:
        - Authenticated user required.
    """
    approval_request = SubmitAgreementForApprovalRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    result = agreement_manager.submit_for_approval(
        approval_request.agreement_id,
        approval_request.role
    )
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement submitted for approval",
        data=result
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def approve_agreement(context: GacoContext, data: dict) -> dict:
    """
    Approves an agreement for a specific role.

    Authentication:
        - Authenticated user required.
    """
    approval_request = ApproveAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    result = agreement_manager.approve_agreement(
        approval_request.agreement_id,
        approval_request.role,
        approval_request.comments,
    )
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement approved",
        data=result
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def terminate_agreement(context: GacoContext, data: dict) -> dict:
    """
    Terminates an active agreement.

    Authentication:
        - Authenticated user required.
    """
    terminate_request = TerminateAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    result = agreement_manager.terminate_agreement(terminate_request.agreement_id)
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement terminated",
        data=result
    )


@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def reject_agreement(context: GacoContext, data: dict) -> dict:
    """
    Rejects an agreement for a specific role.

    Authentication:
        - Authenticated user required.
    """
    reject_request = RejectAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    result = agreement_manager.reject_agreement(
        reject_request.agreement_id,
        reject_request.role,
        reject_request.comments,
    )
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement rejected",
        data=result
    )

@gaco_endpoint(
    require_auth=AuthRequirement.AUTHENTICATED,
    memory=options.MemoryOption.MB_512,
)
def delete_agreement(context: GacoContext, data: dict) -> dict:
    """
    Deletes an agreement (for development purposes).

    Authentication:
        - Authenticated user required.
    """
    delete_request = DeleteAgreementRequest.model_validate(data)
    agreement_manager = AgreementManager(
        db=context.db, auth_context=context.auth_context
    )
    result = agreement_manager.delete_agreement(delete_request.agreement_id)
    return GacoResponse(
        success=True,
        code=200,
        message=f"Agreement deleted",
        data=result
    )


@on_document_deleted_with_auth_context(
    document="agreements/{document_id}",
    region="europe-west3",
    memory=options.MemoryOption.MB_512,
)
def on_agreement_deleted(event: Event[DocumentSnapshot|None]) -> None:
    """
    Cloud function that triggers when an agreement is deleted.
    Keep the agreeemnts and partnerships in sync.
    Rather have it here so direct delete on the collection will trigger this functioh too.
    """

    agreement_id = event.data.id
    db = firestore.client()
    partnership_docs = db.collection(partnership_collection)\
        .where(Partnership.AGREEMENT_ID_FIELD, "==", agreement_id).get()

    for doc in partnership_docs:
        doc.reference.delete()
