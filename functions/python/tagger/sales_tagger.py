from typing import Dict, List, Optional, Any
from firebase_admin import firestore
from firebase_functions import logger

from gaco_framework.managers import BaseGacoManager
from gaco_framework.models import BaseGacoModel
from queries.sales_staging_query_builder import SalesStagingQueryBuilder
from queries.sales_silver_query_builder import SalesSilverQueryBuilder


class SalesDataTagRequest(BaseGacoModel):
    """Request model for sales data tagging configuration"""
    store_id: str
    fields: List[str]  # Fields in the sales model to check against tag rules
    tag_rule: Dict[str, List[str]]  # producer_id -> list of keywords to match
    agreements: List[str]  # List of agreement IDs to consider


class SalesTagger(BaseGacoManager):
    """
    Sales tagger that applies producer tagging rules to sales data based on field content.

    This class processes sales data and tags them with producer IDs when the specified
    fields contain keywords associated with specific producers according to the tag rules.
    """

    def __init__(self, db: firestore.Client, auth_context=None):
        super().__init__(db, auth_context)
        self.sales_staging_query_builder = SalesStagingQueryBuilder(self.db)
        self.sales_silver_query_builder = SalesSilverQueryBuilder(self.db)

    def tag_sales_data(self, tag_request: SalesDataTagRequest) -> Dict[str, Any]:
        """
        Tag sales data based on the provided configuration.

        Args:
            tag_request: Configuration specifying which fields to check and tag rules

        Returns:
            Dictionary containing tagging results and statistics
        """
        logger.info(f"Starting sales data tagging for store: {tag_request.store_id}")

        # Get sales data for the store
        sales_staging_docs = self._get_sales_staging_data(tag_request.store_id)
        sales_silver_docs = self._get_sales_silver_data(tag_request.store_id)

        # Process both staging and silver sales
        staging_results = self._process_sales_documents(
            sales_staging_docs, tag_request, "sales-staging"
        )
        silver_results = self._process_sales_documents(
            sales_silver_docs, tag_request, "sales-silver"
        )

        # Combine results
        total_processed = staging_results["processed"] + silver_results["processed"]
        total_tagged = staging_results["tagged"] + silver_results["tagged"]

        results = {
            "success": True,
            "store_id": tag_request.store_id,
            "total_processed": total_processed,
            "total_tagged": total_tagged,
            "staging_results": staging_results,
            "silver_results": silver_results,
            "tag_rules_applied": len(tag_request.tag_rule)
        }

        logger.info(f"Tagging completed. Processed: {total_processed}, Tagged: {total_tagged}")
        return results

    def _get_sales_staging_data(self, store_id: str) -> List[firestore.DocumentSnapshot]:
        """Get sales staging documents for the store"""
        return list(
            self.sales_staging_query_builder
            .for_store_id(store_id)
            .build()
            .stream()
        )

    def _get_sales_silver_data(self, store_id: str) -> List[firestore.DocumentSnapshot]:
        """Get sales silver documents for the store"""
        return list(
            self.sales_silver_query_builder
            .for_store_id(store_id)
            .build()
            .stream()
        )

    def _process_sales_documents(
        self,
        docs: List[firestore.DocumentSnapshot],
        tag_request: SalesDataTagRequest,
        collection_name: str
    ) -> Dict[str, Any]:
        """
        Process a list of sales documents and apply tagging rules.

        Args:
            docs: List of Firestore document snapshots
            tag_request: Tagging configuration
            collection_name: Name of the collection being processed

        Returns:
            Dictionary with processing results
        """
        processed_count = 0
        tagged_count = 0
        tagged_documents = []

        for doc in docs:
            processed_count += 1
            sale_data = doc.to_dict()

            # Check if this sale should be tagged
            matched_producer_id = self._apply_tag_rules(sale_data, tag_request)

            if matched_producer_id:
                # Update the document with the producer tag
                self._update_sale_with_producer_tag(
                    doc.id, matched_producer_id, collection_name
                )
                tagged_count += 1
                tagged_documents.append({
                    "document_id": doc.id,
                    "producer_id": matched_producer_id,
                    "matched_fields": self._get_matched_fields(sale_data, tag_request, matched_producer_id)
                })

        return {
            "collection": collection_name,
            "processed": processed_count,
            "tagged": tagged_count,
            "tagged_documents": tagged_documents
        }

    def _apply_tag_rules(
        self,
        sale_data: Dict[str, Any],
        tag_request: SalesDataTagRequest
    ) -> Optional[str]:
        """
        Apply tag rules to a sale and return the matching producer ID if any.

        Args:
            sale_data: Sale document data
            tag_request: Tagging configuration

        Returns:
            Producer ID if a match is found, None otherwise
        """
        # Check each producer's keywords against the specified fields
        for producer_id, keywords in tag_request.tag_rule.items():
            if self._check_keywords_match(sale_data, tag_request.fields, keywords):
                logger.info(f"Sale matched producer {producer_id} with keywords: {keywords}")
                return producer_id

        return None

    def _check_keywords_match(
        self,
        sale_data: Dict[str, Any],
        fields: List[str],
        keywords: List[str]
    ) -> bool:
        """
        Check if any of the keywords match content in the specified fields.

        Args:
            sale_data: Sale document data
            fields: List of field names to check
            keywords: List of keywords to search for

        Returns:
            True if any keyword matches, False otherwise
        """
        # Get content from specified fields
        field_content = []
        for field in fields:
            if field in sale_data and sale_data[field]:
                field_content.append(str(sale_data[field]).lower())

        # Check if any keyword matches any field content
        combined_content = " ".join(field_content)

        for keyword in keywords:
            if keyword.lower() in combined_content:
                logger.info(f"Keyword '{keyword}' found in fields {fields}")
                return True

        return False

    def _get_matched_fields(
        self,
        sale_data: Dict[str, Any],
        tag_request: SalesDataTagRequest,
        producer_id: str
    ) -> List[str]:
        """Get the fields that matched for the given producer"""
        matched_fields = []
        keywords = tag_request.tag_rule[producer_id]

        for field in tag_request.fields:
            if field in sale_data and sale_data[field]:
                field_content = str(sale_data[field]).lower()
                for keyword in keywords:
                    if keyword.lower() in field_content:
                        matched_fields.append(field)
                        break

        return matched_fields

    def _update_sale_with_producer_tag(
        self,
        document_id: str,
        producer_id: str,
        collection_name: str
    ) -> None:
        """
        Update a sale document with the producer tag.

        Args:
            document_id: Document ID to update
            producer_id: Producer ID to tag with
            collection_name: Collection name (sales-staging or sales-silver)
        """
        try:
            # Prepare update data
            update_data = {
                "producer_id": producer_id,
                "updated_at": firestore.SERVER_TIMESTAMP,
                "tagged_by": "sales_tagger"
            }

            # Update the document
            self.db.collection(collection_name).document(document_id).update(update_data)

            logger.info(f"Updated {collection_name} document {document_id} with producer_id: {producer_id}")

        except Exception as e:
            logger.error(f"Failed to update document {document_id}: {str(e)}")
            raise