# Sales Tagger

The Sales Tagger is a component that automatically tags sales data with producer IDs based on configurable keyword matching rules.

## Overview

The `SalesTagger` class processes sales data from both `sales-staging` and `sales-silver` collections and applies producer tags when specified keywords are found in designated fields.

## Key Components

### SalesTagger Class

The main class that handles the tagging logic:

- **Location**: `functions/python/tagger/sales_tagger.py`
- **Extends**: `BaseGacoManager` from the Gaco Framework
- **Purpose**: Tag sales data with producer IDs based on keyword matching

### SalesDataTagRequest Model

A Pydantic model that defines the tagging configuration:

```python
class SalesDataTagRequest(BaseGacoModel):
    store_id: str                    # Store ID to process sales for
    fields: List[str]                # Fields to search in (e.g., ["title", "vendor"])
    tag_rule: Dict[str, List[str]]   # producer_id -> list of keywords
    agreements: List[str]            # Related agreement IDs
```

## Usage

### Basic Example

```python
from firebase_admin import firestore
from tagger.sales_tagger import SalesTagger, SalesDataTagRequest

# Initialize
db = firestore.client()
sales_tagger = SalesTagger(db)

# Create tagging configuration
tag_request = SalesDataTagRequest(
    store_id="store_123",
    fields=["title"],
    tag_rule={
        "producer_1": ["artisan", "handmade"],
        "producer_2": ["vintage", "antique"]
    },
    agreements=["agreement_1"]
)

# Execute tagging
result = sales_tagger.tag_sales_data(tag_request)
```

### Configuration Format

The `tag_rule` dictionary maps producer IDs to lists of keywords:

```python
tag_rule = {
    "producer_abc123": ["keyword1", "keyword2", "phrase with spaces"],
    "producer_def456": ["another_keyword", "case insensitive match"]
}
```

**Key Features:**
- **Case-insensitive matching**: Keywords match regardless of case
- **Partial matching**: Keywords can appear anywhere within the field content
- **Multi-field support**: Can search across multiple fields simultaneously
- **First-match wins**: The first producer whose keywords match will be assigned

## How It Works

1. **Data Retrieval**: Fetches sales documents from both `sales-staging` and `sales-silver` collections for the specified store
2. **Keyword Matching**: For each sale, checks if any keywords from the tag rules appear in the specified fields
3. **Tagging**: Updates matching documents with the producer ID and metadata
4. **Results**: Returns detailed statistics about the tagging process

## Database Updates

When a match is found, the sales document is updated with:

```python
{
    "producer_id": "matched_producer_id",
    "updated_at": firestore.SERVER_TIMESTAMP,
    "tagged_by": "sales_tagger"
}
```

## Return Format

The `tag_sales_data` method returns a comprehensive result dictionary:

```python
{
    "success": True,
    "store_id": "store_123",
    "total_processed": 150,
    "total_tagged": 25,
    "staging_results": {
        "collection": "sales-staging",
        "processed": 75,
        "tagged": 12,
        "tagged_documents": [
            {
                "document_id": "doc_1",
                "producer_id": "producer_1",
                "matched_fields": ["title"]
            }
        ]
    },
    "silver_results": {
        "collection": "sales-silver",
        "processed": 75,
        "tagged": 13,
        "tagged_documents": [...]
    },
    "tag_rules_applied": 2
}
```

## Testing

Comprehensive tests are available in `tests/tagger/test_sales_tagger.py`:

```bash
# Run tests using the virtual environment
./venv/bin/python -m pytest tests/tagger/test_sales_tagger.py -v
```

## Integration with Existing Test

The existing test in `tests/services/test_sales_data_tagger.py` has been updated to use the new `SalesTagger` class:

```python
# Create the tagging request
sales_data_tag_request = SalesDataTagRequest(
    store_id=store_id,
    fields=["title"],
    tag_rule={
        producerId: ['producer_1', 'producer 1']
    },
    agreements=["agreement_id_1"]
)

# Execute tagging
sales_tagger = SalesTagger(db, user_with_root_account)
tagging_result = sales_tagger.tag_sales_data(sales_data_tag_request)
```

## Error Handling

The tagger includes proper error handling:
- Logs successful matches and updates
- Catches and logs database update failures
- Returns success/failure status in results

## Dependencies

- `firebase_admin`: For Firestore database operations
- `gaco_framework`: For base manager functionality and models
- `queries.sales_staging_query_builder`: For querying sales-staging collection
- `queries.sales_silver_query_builder`: For querying sales-silver collection

## Future Enhancements

Potential improvements could include:
- Regex pattern matching support
- Weighted keyword matching
- Batch update operations for better performance
- Audit trail for tagging history
- Support for excluding certain keywords
