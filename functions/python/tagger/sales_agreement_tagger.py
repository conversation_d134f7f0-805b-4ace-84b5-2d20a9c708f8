from gaco_framework.managers import BaseGacoManager
from constants.collections import (
    sales_staging_collection, 
    agreement_collection
)
from firebase_admin import firestore


class SalesAgreementTagger(BaseGacoManager):

    sales_staging_collection = sales_staging_collection
    agreement_collection = agreement_collection

    def __init__(self, db: firestore.Client, auth_context=None):
        super().__init__(db, auth_context)
        self._get_collection_ref = self._get_collection(self.sales_staging_collection)
        self._get_agreement_collection_ref = self._get_collection(self.agreement_collection)
    pass