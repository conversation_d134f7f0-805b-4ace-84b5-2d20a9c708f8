# new data cleaning function
from datetime import timedelta
import pytest
from flows.force_active_partnership import ForceActivePartnershipFlow
from models.requests.sanitization_request import ForceAgreementRequest


def test_sales_data_tagger(
        n_sales_stagin, 
        old_start_date, 
        old_end_date, 
        db, 
        user_with_root_account
    ):
    import pdb; pdb.set_trace()

    # missing objects
    sampel_0 = n_sales_stagin[0]
    
    sample_0_doc = db.collection("sales-staging").document(sampel_0).get()

    sample_0_data = sample_0_doc.to_dict()

    # old_start_date
    # old_end_date - timedelta(days=700)


    store_id = sample_0_data['storeId']

    # 1. make an agreement + producer
    request = ForceAgreementRequest(
        store_id=store_id,
        title="test-title",
        effective_date=old_start_date,
        expiration_date=old_end_date - timedelta(days=700),
        commission=100,
        document_url="document_url",
        display_name="producer one",
        email="<EMAIL>",
        tax_a2="NL"
    )

    result = ForceActivePartnershipFlow(db, user_with_root_account)\
        .force_create_active_partnership(request)
    # result = flow.force_create_active_partnership(request)

    import pdb; pdb.set_trace()

    producerId = result['producer_id']

    sales_data_tag_request = {
        "storeId": store_id,
        "fields": ["title"],
        "tagRule": {
            producerId: ['producer_1', 'producer 1']
        },
        "agreements": ["agreement_id_1"],
    }


