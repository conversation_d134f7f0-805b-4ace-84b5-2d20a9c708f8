import pytest
from unittest.mock import Mock, patch
from firebase_admin import firestore

from tagger.sales_tagger import SalesTagger, SalesStagingTagRequest


@pytest.fixture
def mock_db():
    """Mock Firestore database client"""
    return Mock(spec=firestore.Client)


@pytest.fixture
def sales_tagger(mock_db):
    """Create SalesTagger instance with mocked dependencies"""
    return SalesTagger(mock_db)


@pytest.fixture
def sample_tag_request():
    """Sample tagging request configuration"""
    return SalesStagingTagRequest(
        store_id="test_store_123",
        fields=["title"],
        tag_rule={
            "producer_1": ["producer_1", "producer 1"],
            "producer_2": ["artisan", "handmade"]
        },
        agreements=["agreement_id_1"]
    )


@pytest.fixture
def sample_sales_docs():
    """Sample sales documents for testing"""
    docs = []
    
    # Document that should match producer_1
    doc1 = Mock()
    doc1.id = "sale_1"
    doc1.to_dict.return_value = {
        "storeId": "test_store_123",
        "title": "Beautiful item by producer 1",
        "vendor": "some_vendor",
        "quantity": 2,
        "unitPrice": 25.0
    }
    docs.append(doc1)
    
    # Document that should match producer_2
    doc2 = Mock()
    doc2.id = "sale_2"
    doc2.to_dict.return_value = {
        "storeId": "test_store_123",
        "title": "Handmade bracelet by artisan",
        "vendor": "another_vendor",
        "quantity": 1,
        "unitPrice": 50.0
    }
    docs.append(doc2)
    
    # Document that should not match any producer
    doc3 = Mock()
    doc3.id = "sale_3"
    doc3.to_dict.return_value = {
        "storeId": "test_store_123",
        "title": "Regular product",
        "vendor": "regular_vendor",
        "quantity": 3,
        "unitPrice": 15.0
    }
    docs.append(doc3)
    
    return docs


class TestSalesTagger:
    """Test cases for SalesTagger class"""
    
    def test_initialization(self, mock_db):
        """Test SalesTagger initialization"""
        tagger = SalesTagger(mock_db)
        assert tagger.db == mock_db
        assert hasattr(tagger, 'sales_staging_query_builder')
        assert hasattr(tagger, 'sales_silver_query_builder')
    
    def test_check_keywords_match_positive(self, sales_tagger):
        """Test keyword matching with positive cases"""
        sale_data = {
            "title": "Beautiful item by producer 1",
            "vendor": "some_vendor"
        }
        
        # Should match "producer 1"
        result = sales_tagger._check_keywords_match(
            sale_data, ["title"], ["producer_1", "producer 1"]
        )
        assert result is True
        
        # Should match "producer_1" (case insensitive) - but "Producer_1" != "producer 1"
        # Let's test with a keyword that actually appears in the text
        result = sales_tagger._check_keywords_match(
            sale_data, ["title"], ["Beautiful"]
        )
        assert result is True
    
    def test_check_keywords_match_negative(self, sales_tagger):
        """Test keyword matching with negative cases"""
        sale_data = {
            "title": "Regular product",
            "vendor": "regular_vendor"
        }
        
        # Should not match
        result = sales_tagger._check_keywords_match(
            sale_data, ["title"], ["producer_1", "producer 1"]
        )
        assert result is False
    
    def test_apply_tag_rules(self, sales_tagger, sample_tag_request):
        """Test applying tag rules to sales data"""
        # Test data that should match producer_1
        sale_data_1 = {
            "title": "Item by producer 1",
            "vendor": "vendor1"
        }
        result = sales_tagger._apply_tag_rules(sale_data_1, sample_tag_request)
        assert result == "producer_1"

        # Test data that should match producer_2
        sale_data_2 = {
            "title": "Handmade jewelry",
            "vendor": "vendor2"
        }
        result = sales_tagger._apply_tag_rules(sale_data_2, sample_tag_request)
        assert result == "producer_2"

        # Test data that should not match any producer
        sale_data_3 = {
            "title": "Regular item",
            "vendor": "vendor3"
        }
        result = sales_tagger._apply_tag_rules(sale_data_3, sample_tag_request)
        assert result is None

    def test_multiple_producers_first_match_wins(self, sales_tagger):
        """Test that first matching producer wins when multiple could match"""
        multi_producer_request = SalesStagingTagRequest(
            store_id="test_store",
            fields=["title"],
            tag_rule={
                "producer_A": ["craft", "handmade"],
                "producer_B": ["craft", "artisan"],  # Both have "craft"
                "producer_C": ["jewelry", "accessories"]
            },
            agreements=["agreement_1"]
        )

        # This should match producer_A first (even though producer_B also matches "craft")
        sale_data = {
            "title": "Beautiful craft item",
            "vendor": "vendor1"
        }
        result = sales_tagger._apply_tag_rules(sale_data, multi_producer_request)
        assert result == "producer_A"  # First match wins
    
    def test_get_matched_fields(self, sales_tagger, sample_tag_request):
        """Test getting matched fields for a producer"""
        sale_data = {
            "title": "Beautiful handmade item by producer 1",
            "vendor": "some_vendor"
        }
        
        # Should match title field for producer_1
        matched_fields = sales_tagger._get_matched_fields(
            sale_data, sample_tag_request, "producer_1"
        )
        assert "title" in matched_fields
        
        # Should also match title field for producer_2 (handmade keyword)
        matched_fields = sales_tagger._get_matched_fields(
            sale_data, sample_tag_request, "producer_2"
        )
        assert "title" in matched_fields
    
    @patch('tagger.sales_tagger.SalesTagger._get_sales_staging_data')
    @patch('tagger.sales_tagger.SalesTagger._get_sales_silver_data')
    @patch('tagger.sales_tagger.SalesTagger._update_sale_with_producer_tag')
    def test_tag_sales_data_integration(
        self, 
        mock_update, 
        mock_get_silver, 
        mock_get_staging, 
        sales_tagger, 
        sample_tag_request, 
        sample_sales_docs
    ):
        """Test the main tag_sales_data method"""
        # Setup mocks
        mock_get_staging.return_value = sample_sales_docs[:2]  # First 2 docs
        mock_get_silver.return_value = [sample_sales_docs[2]]   # Last doc
        
        # Execute
        result = sales_tagger.tag_sales_data(sample_tag_request)
        
        # Verify results
        assert result["success"] is True
        assert result["store_id"] == "test_store_123"
        assert result["total_processed"] == 3
        assert result["total_tagged"] == 2  # Only first 2 should match
        
        # Verify update was called for matching documents
        assert mock_update.call_count == 2
        
        # Verify the calls were made with correct parameters
        calls = mock_update.call_args_list
        assert calls[0][0] == ("sale_1", "producer_1", "sales-staging")
        assert calls[1][0] == ("sale_2", "producer_2", "sales-staging")
    
    def test_update_sale_with_producer_tag(self, mock_db):
        """Test updating a sale document with producer tag"""
        # Create a fresh tagger instance for this test to avoid mock conflicts
        tagger = SalesTagger(mock_db)

        # Setup mock collection and document
        mock_collection = Mock()
        mock_document = Mock()
        mock_db.collection.return_value = mock_collection
        mock_collection.document.return_value = mock_document

        # Reset the mock to clear any calls from initialization
        mock_db.reset_mock()

        # Execute
        tagger._update_sale_with_producer_tag(
            "test_doc_id", "producer_123", "sales-staging"
        )

        # Verify calls
        mock_db.collection.assert_called_once_with("sales-staging")
        mock_collection.document.assert_called_once_with("test_doc_id")
        mock_document.update.assert_called_once()

        # Verify update data structure
        update_call_args = mock_document.update.call_args[0][0]
        assert update_call_args["producer_id"] == "producer_123"
        assert update_call_args["tagged_by"] == "sales_tagger"
        assert "updated_at" in update_call_args


class TestSalesStagingTagRequest:
    """Test cases for SalesStagingTagRequest model"""
    
    def test_valid_request_creation(self):
        """Test creating a valid tag request"""
        request = SalesStagingTagRequest(
            store_id="store_123",
            fields=["title", "vendor"],
            tag_rule={"producer_1": ["keyword1", "keyword2"]},
            agreements=["agreement_1"]
        )
        
        assert request.store_id == "store_123"
        assert request.fields == ["title", "vendor"]
        assert request.tag_rule == {"producer_1": ["keyword1", "keyword2"]}
        assert request.agreements == ["agreement_1"]
    
    def test_request_serialization(self):
        """Test request model serialization"""
        request = SalesStagingTagRequest(
            store_id="store_123",
            fields=["title"],
            tag_rule={"producer_1": ["keyword1"]},
            agreements=["agreement_1"]
        )
        
        # Test model_dump works
        data = request.model_dump()
        assert isinstance(data, dict)
        assert data["storeId"] == "store_123"  # Should be camelCase
        assert data["fields"] == ["title"]
        assert data["tagRule"] == {"producer_1": ["keyword1"]}
